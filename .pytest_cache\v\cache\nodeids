["tests/test_clients.py::TestGetLlmClient::test_get_llm_client", "tests/test_clients.py::TestGetMem0Client::test_get_mem0_client_with_ollama", "tests/test_clients.py::TestGetMem0Client::test_get_mem0_client_with_openai", "tests/test_clients.py::TestGetMem0Client::test_get_mem0_client_with_openrouter", "tests/test_clients.py::TestGetSupabaseClient::test_get_supabase_client", "tests/test_code_execution.py::TestExecuteSafeCodeTool::test_execute_safe_code_success", "tests/test_code_execution.py::TestExecuteSafeCodeTool::test_execute_safe_code_with_allowed_modules", "tests/test_code_execution.py::TestExecuteSafeCodeTool::test_execute_safe_code_with_complex_operations", "tests/test_code_execution.py::TestExecuteSafeCodeTool::test_execute_safe_code_with_disallowed_modules", "tests/test_code_execution.py::TestExecuteSafeCodeTool::test_execute_safe_code_with_exception", "tests/test_image_analysis.py::TestImageAnalysisTool::test_image_analysis_tool_exception", "tests/test_image_analysis.py::TestImageAnalysisTool::test_image_analysis_tool_no_document", "tests/test_image_analysis.py::TestImageAnalysisTool::test_image_analysis_tool_no_file_contents", "tests/test_image_analysis.py::TestImageAnalysisTool::test_image_analysis_tool_success", "tests/test_main.py::test_main_function_prints_correct_message", "tests/test_rag.py::TestDocumentTools::test_get_document_content_tool_exception", "tests/test_rag.py::TestDocumentTools::test_get_document_content_tool_no_content", "tests/test_rag.py::TestDocumentTools::test_get_document_content_tool_success", "tests/test_rag.py::TestDocumentTools::test_list_documents_tool_exception", "tests/test_rag.py::TestDocumentTools::test_list_documents_tool_success", "tests/test_rag.py::TestDocumentTools::test_retrieve_relevant_documents_tool_exception", "tests/test_rag.py::TestDocumentTools::test_retrieve_relevant_documents_tool_no_results", "tests/test_rag.py::TestDocumentTools::test_retrieve_relevant_documents_tool_success", "tests/test_rag.py::TestEmbeddingTools::test_get_embedding_exception", "tests/test_rag.py::TestEmbeddingTools::test_get_embedding_success", "tests/test_sql_query.py::TestSqlQueryTool::test_execute_sql_query_tool_error_from_supabase", "tests/test_sql_query.py::TestSqlQueryTool::test_execute_sql_query_tool_general_exception", "tests/test_sql_query.py::TestSqlQueryTool::test_execute_sql_query_tool_success", "tests/test_sql_query.py::TestSqlQueryTool::test_execute_sql_query_tool_write_operation_detected", "tests/test_web_search.py::TestWebSearchTools::test_brave_web_search_no_results", "tests/test_web_search.py::TestWebSearchTools::test_brave_web_search_success", "tests/test_web_search.py::TestWebSearchTools::test_searxng_web_search_no_results", "tests/test_web_search.py::TestWebSearchTools::test_searxng_web_search_success", "tests/test_web_search.py::TestWebSearchTools::test_web_search_tool_exception", "tests/test_web_search.py::TestWebSearchTools::test_web_search_tool_with_brave", "tests/test_web_search.py::TestWebSearchTools::test_web_search_tool_with_searxng"]