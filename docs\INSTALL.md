# Installation

## System Requirements
- Python 3.11 or higher
- Supabase project with pgvector extension enabled
- 4GB+ RAM for local LLM deployment (if using Ollama)

## Deployment Options

### Cloud Deployment
**Required Services:**
- Supabase project with pgvector enabled
- OpenAI API key or OpenRouter account
- Brave Search API key (optional: use SearXNG instead)

### Local Deployment
**Required Software:**
- [Local AI Package](https://github.com/coleam00/local-ai-packaged) (all-in-one)
  - OR manually: Ollama + SearXNG
- Supabase project (cloud database)
- Deno (for MCP code execution server)

### Hybrid Deployment
- Cloud LLM (OpenAI/OpenRouter) + Supabase database
- Local LLM (Ollama) + Supabase database

## Quick Start

1. **Install Agent Dependencies**
   ```bash
   cd base_agent
   python -m venv .venv
   # Windows: .venv\Scripts\activate
   # Unix: source .venv/bin/activate
   pip install -r requirements.txt
   ```

2. **Install RAG Pipeline Dependencies**
   ```bash
   cd RAG_Pipeline
   python -m venv .venv
   # Windows: .venv\Scripts\activate
   # Unix: source .venv/bin/activate
   pip install -r requirements.txt
   ```

3. **Configure Environment**
   ```bash
   # Copy example configuration
   cp .env.example .env
   # Edit .env with your service credentials
   ```
