[project]
name = "luminari-ai-agent"
version = "0.1.0"
description = "Production-ready AI agent with RAG, memory, and multi-tool capabilities"
requires-python = ">=3.11"
dependencies = [
    "pydantic-ai>=0.0.10",
    "pydantic>=2.5.0",
    "streamlit>=1.29.0",
    "python-dotenv>=1.0.0",
    "openai>=1.10.0",
    "ollama>=0.1.0",
    "mem0ai>=0.0.10",
    "supabase>=2.3.0",
    "vecs>=0.4.0",
    "brave-search>=0.1.0",
    "pypdf>=3.17.0",
    "google-api-python-client>=2.100.0",
    "structlog>=24.0.0",
    "click>=8.1.0",
    "httpx>=0.24.0",
    "asyncpg>=0.28.0",
    "numpy>=1.24.0",
    "aiofiles>=23.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.12.0",
    "ruff>=0.1.9",
    "mypy>=1.8.0",
    "pre-commit>=3.6.0",
]
rag = [
    "watchdog>=3.0.0",
    "python-docx>=1.1.0",
    "openpyxl>=3.1.2",
    "unstructured>=0.11.0",
]

[project.scripts]
luminari-agent = "src.__main__:main"
luminari-rag = "src.rag_pipeline.pipeline:main"
luminari-ui = "src.ui.streamlit_app:main"

[tool.ruff]
line-length = 100
target-version = "py311"
fix = true

[tool.black]
line-length = 100
target-version = ['py311', 'py312']

[tool.pytest.ini_options]
testpaths = ["tests"]
addopts = [
    "--verbose",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-fail-under=75",
    "--asyncio-mode=auto",
]
