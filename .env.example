# ===== SUPABASE (Primary Database) =====
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your_supabase_service_role_key

# ===== LLM CONFIGURATION =====
LLM_PROVIDER=openai                           # openai, openrouter, ollama
LLM_BASE_URL=https://api.openai.com/v1
LLM_API_KEY=your_api_key_here
LLM_CHOICE=gpt-4o-mini

# ===== EMBEDDING CONFIGURATION =====
EMBEDDING_PROVIDER=openai
EMBEDDING_BASE_URL=https://api.openai.com/v1
EMBEDDING_API_KEY=your_api_key_here
EMBEDDING_MODEL_CHOICE=text-embedding-3-small

# ===== DATABASE (Uses Supabase) =====
DATABASE_URL=\                   # Points to Supabase

# ===== WEB SEARCH =====
BRAVE_API_KEY=                                # Optional: leave empty for SearXNG
SEARXNG_BASE_URL=http://localhost:8080

# ===== GOOGLE DRIVE (Optional) =====
GOOGLE_DRIVE_FOLDER_ID=
GOOGLE_DRIVE_CREDENTIALS_PATH=config/google_credentials.json

# ===== AGENT SETTINGS =====
AGENT_NAME=Luminari
AGENT_TEMPERATURE=0.7

# ===== FEATURE FLAGS =====
ENABLE_CODE_EXECUTION=false
ENABLE_WEB_SEARCH=true
ENABLE_IMAGE_ANALYSIS=true
ENABLE_LONG_TERM_MEMORY=true
ENABLE_RAG=true

# ===== VISION MODEL =====
VISION_MODEL=gpt-4o-mini
VISION_LLM_CHOICE=gpt-4o-mini
